<?php

namespace models\composite\oDrawManagement\tracking;

use models\composite\oDrawManagement\dto\base\BaseDTO;

/**
 * Property Change Tracking System
 * 
 * Tracks property changes for better debugging, auditing, 
 * and optimized database updates
 */
class ChangeTracker
{
    /**
     * @var array Tracked objects
     */
    private array $trackedObjects = [];

    /**
     * @var array Change history
     */
    private array $changeHistory = [];

    /**
     * @var bool Whether to enable detailed logging
     */
    private bool $detailedLogging = false;

    /**
     * Constructor
     * 
     * @param bool $detailedLogging Enable detailed logging
     */
    public function __construct(bool $detailedLogging = false)
    {
        $this->detailedLogging = $detailedLogging;
    }

    /**
     * Start tracking an object
     * 
     * @param BaseDTO $object Object to track
     * @param string|null $identifier Optional identifier for the object
     * @return string Tracking ID
     */
    public function track(BaseDTO $object, ?string $identifier = null): string
    {
        $trackingId = $identifier ?? uniqid('track_', true);
        
        $this->trackedObjects[$trackingId] = [
            'object' => $object,
            'originalState' => $object->toArray(),
            'startTime' => microtime(true),
            'identifier' => $identifier
        ];

        if ($this->detailedLogging) {
            $this->logChange($trackingId, 'TRACKING_STARTED', null, null, [
                'class' => get_class($object),
                'properties_count' => count($object->toArray())
            ]);
        }

        return $trackingId;
    }

    /**
     * Stop tracking an object
     * 
     * @param string $trackingId Tracking ID
     * @return array|null Final change summary
     */
    public function stopTracking(string $trackingId): ?array
    {
        if (!isset($this->trackedObjects[$trackingId])) {
            return null;
        }

        $tracked = $this->trackedObjects[$trackingId];
        $object = $tracked['object'];
        $currentState = $object->toArray();
        $changes = $this->compareStates($tracked['originalState'], $currentState);

        $summary = [
            'trackingId' => $trackingId,
            'identifier' => $tracked['identifier'],
            'class' => get_class($object),
            'duration' => microtime(true) - $tracked['startTime'],
            'totalChanges' => count($changes),
            'changes' => $changes,
            'originalState' => $tracked['originalState'],
            'finalState' => $currentState
        ];

        if ($this->detailedLogging) {
            $this->logChange($trackingId, 'TRACKING_STOPPED', null, null, $summary);
        }

        unset($this->trackedObjects[$trackingId]);
        return $summary;
    }

    /**
     * Take a snapshot of current state
     * 
     * @param string $trackingId Tracking ID
     * @param string|null $label Optional label for the snapshot
     * @return array|null Snapshot data
     */
    public function snapshot(string $trackingId, ?string $label = null): ?array
    {
        if (!isset($this->trackedObjects[$trackingId])) {
            return null;
        }

        $tracked = $this->trackedObjects[$trackingId];
        $object = $tracked['object'];
        $currentState = $object->toArray();
        $changes = $this->compareStates($tracked['originalState'], $currentState);

        $snapshot = [
            'trackingId' => $trackingId,
            'label' => $label,
            'timestamp' => microtime(true),
            'state' => $currentState,
            'changesSinceStart' => $changes,
            'changesCount' => count($changes)
        ];

        if ($this->detailedLogging) {
            $this->logChange($trackingId, 'SNAPSHOT_TAKEN', null, null, $snapshot);
        }

        return $snapshot;
    }

    /**
     * Compare two states and return differences
     * 
     * @param array $originalState Original state
     * @param array $currentState Current state
     * @return array Array of changes
     */
    private function compareStates(array $originalState, array $currentState): array
    {
        $changes = [];
        
        // Check for modified and new properties
        foreach ($currentState as $property => $currentValue) {
            $originalValue = $originalState[$property] ?? null;
            
            if ($this->valuesAreDifferent($originalValue, $currentValue)) {
                $changes[$property] = [
                    'type' => isset($originalState[$property]) ? 'modified' : 'added',
                    'from' => $originalValue,
                    'to' => $currentValue,
                    'timestamp' => microtime(true)
                ];
            }
        }
        
        // Check for removed properties
        foreach ($originalState as $property => $originalValue) {
            if (!array_key_exists($property, $currentState)) {
                $changes[$property] = [
                    'type' => 'removed',
                    'from' => $originalValue,
                    'to' => null,
                    'timestamp' => microtime(true)
                ];
            }
        }
        
        return $changes;
    }

    /**
     * Check if two values are different
     * 
     * @param mixed $value1 First value
     * @param mixed $value2 Second value
     * @return bool True if different
     */
    private function valuesAreDifferent($value1, $value2): bool
    {
        // Handle arrays recursively
        if (is_array($value1) && is_array($value2)) {
            return $this->arraysAreDifferent($value1, $value2);
        }
        
        // Handle null comparisons
        if ($value1 === null || $value2 === null) {
            return $value1 !== $value2;
        }
        
        // Handle numeric comparisons with tolerance
        if (is_numeric($value1) && is_numeric($value2)) {
            return abs((float)$value1 - (float)$value2) > 0.001;
        }
        
        return $value1 !== $value2;
    }

    /**
     * Check if two arrays are different
     * 
     * @param array $array1 First array
     * @param array $array2 Second array
     * @return bool True if different
     */
    private function arraysAreDifferent(array $array1, array $array2): bool
    {
        if (count($array1) !== count($array2)) {
            return true;
        }
        
        foreach ($array1 as $key => $value) {
            if (!array_key_exists($key, $array2)) {
                return true;
            }
            
            if ($this->valuesAreDifferent($value, $array2[$key])) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Log a change event
     * 
     * @param string $trackingId Tracking ID
     * @param string $eventType Event type
     * @param string|null $property Property name
     * @param mixed $oldValue Old value
     * @param mixed $newValue New value
     * @return void
     */
    private function logChange(string $trackingId, string $eventType, ?string $property, $oldValue, $newValue): void
    {
        $this->changeHistory[] = [
            'trackingId' => $trackingId,
            'eventType' => $eventType,
            'property' => $property,
            'oldValue' => $oldValue,
            'newValue' => $newValue,
            'timestamp' => microtime(true),
            'backtrace' => $this->detailedLogging ? debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5) : null
        ];
    }

    /**
     * Get change history for a specific tracking ID
     * 
     * @param string $trackingId Tracking ID
     * @return array Change history
     */
    public function getChangeHistory(string $trackingId): array
    {
        return array_filter($this->changeHistory, function($entry) use ($trackingId) {
            return $entry['trackingId'] === $trackingId;
        });
    }

    /**
     * Get all change history
     * 
     * @return array Complete change history
     */
    public function getAllChangeHistory(): array
    {
        return $this->changeHistory;
    }

    /**
     * Clear change history
     * 
     * @return void
     */
    public function clearHistory(): void
    {
        $this->changeHistory = [];
    }

    /**
     * Get currently tracked objects
     * 
     * @return array Tracked objects info
     */
    public function getTrackedObjects(): array
    {
        $info = [];
        
        foreach ($this->trackedObjects as $trackingId => $tracked) {
            $info[$trackingId] = [
                'identifier' => $tracked['identifier'],
                'class' => get_class($tracked['object']),
                'startTime' => $tracked['startTime'],
                'duration' => microtime(true) - $tracked['startTime']
            ];
        }
        
        return $info;
    }

    /**
     * Generate optimized update data (only changed properties)
     * 
     * @param string $trackingId Tracking ID
     * @return array|null Optimized update data
     */
    public function getOptimizedUpdateData(string $trackingId): ?array
    {
        if (!isset($this->trackedObjects[$trackingId])) {
            return null;
        }

        $tracked = $this->trackedObjects[$trackingId];
        $object = $tracked['object'];
        $currentState = $object->toArray();
        $changes = $this->compareStates($tracked['originalState'], $currentState);

        $updateData = [];
        foreach ($changes as $property => $change) {
            if ($change['type'] !== 'removed') {
                $updateData[$property] = $change['to'];
            }
        }

        return $updateData;
    }

    /**
     * Check if object has changes
     * 
     * @param string $trackingId Tracking ID
     * @return bool True if object has changes
     */
    public function hasChanges(string $trackingId): bool
    {
        if (!isset($this->trackedObjects[$trackingId])) {
            return false;
        }

        $tracked = $this->trackedObjects[$trackingId];
        $object = $tracked['object'];
        $currentState = $object->toArray();
        $changes = $this->compareStates($tracked['originalState'], $currentState);

        return !empty($changes);
    }
}
