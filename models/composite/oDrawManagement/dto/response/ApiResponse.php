<?php

namespace models\composite\oDrawManagement\dto\response;

use models\composite\oDrawManagement\dto\base\BaseDTO;

/**
 * Standard API Response DTO
 * 
 * Provides consistent structure for all API responses
 */
class ApiResponse extends BaseDTO
{
    public bool $success = false;
    public string $message = '';
    public mixed $data = null;
    public array $errors = [];
    public ?string $timestamp = null;

    /**
     * Create successful response
     * 
     * @param mixed $data Response data
     * @param string $message Success message
     * @return static
     */
    public static function success($data = null, string $message = 'Operation completed successfully'): static
    {
        $response = new static();
        $response->success = true;
        $response->message = $message;
        $response->data = $data;
        $response->timestamp = date('c');
        
        return $response;
    }

    /**
     * Create error response
     * 
     * @param string $message Error message
     * @param array $errors Detailed errors
     * @param mixed $data Optional data
     * @return static
     */
    public static function error(string $message, array $errors = [], $data = null): static
    {
        $response = new static();
        $response->success = false;
        $response->message = $message;
        $response->errors = $errors;
        $response->data = $data;
        $response->timestamp = date('c');
        
        return $response;
    }

    /**
     * Create validation error response
     * 
     * @param array $validationErrors Validation errors
     * @param string $message Error message
     * @return static
     */
    public static function validationError(array $validationErrors, string $message = 'Validation failed'): static
    {
        return static::error($message, $validationErrors);
    }

    /**
     * Check if response has errors
     * 
     * @return bool
     */
    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    /**
     * Add error to response
     * 
     * @param string $error Error message
     * @return void
     */
    public function addError(string $error): void
    {
        $this->errors[] = $error;
        $this->success = false;
    }

    /**
     * Add multiple errors to response
     * 
     * @param array $errors Error messages
     * @return void
     */
    public function addErrors(array $errors): void
    {
        $this->errors = array_merge($this->errors, $errors);
        if (!empty($errors)) {
            $this->success = false;
        }
    }

    /**
     * Set response data
     * 
     * @param mixed $data Response data
     * @return void
     */
    public function setData($data): void
    {
        $this->data = $data;
    }

    /**
     * Override toArray to handle data serialization
     * 
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    public function toArray(bool $includeNulls = true): array
    {
        $result = [
            'success' => $this->success,
            'message' => $this->message,
            'errors' => $this->errors,
            'timestamp' => $this->timestamp
        ];

        // Handle data serialization
        if ($this->data !== null) {
            if (is_object($this->data) && method_exists($this->data, 'toArray')) {
                $result['data'] = $this->data->toArray($includeNulls);
            } elseif (is_array($this->data)) {
                $result['data'] = $this->convertArrayToArray($this->data, $includeNulls);
            } else {
                $result['data'] = $this->data;
            }
        } elseif ($includeNulls) {
            $result['data'] = null;
        }

        return $result;
    }
}
