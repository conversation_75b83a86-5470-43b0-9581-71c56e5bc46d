<?php

namespace models\composite\oDrawManagement\dto\shared;

use models\composite\oDrawManagement\dto\base\ValidatableDTO;
use models\composite\oDrawManagement\DrawRequest;

/**
 * Draw Request Data DTO
 * 
 * Represents draw request data structure used across the draw management system
 */
class DrawRequestData extends ValidatableDTO
{
    public ?int $id = null;
    public ?int $LMRId = null;
    public string $status = DrawRequest::STATUS_NEW;
    public ?int $sowApproved = null;
    public ?int $isDrawRequest = null;
    public ?string $submittedAt = null;
    public ?string $approvedAt = null;
    public ?string $rejectedAt = null;
    public string $rejectionReason = '';
    public float $amountRequested = 0.0;
    public float $amountApproved = 0.0;
    public string $lenderNotes = '';
    public ?string $createdAt = null;
    public ?string $updatedAt = null;
    
    /**
     * @var CategoryData[] Array of categories
     */
    public array $categories = [];

    /**
     * Define validation rules
     * 
     * @return array
     */
    protected function getValidationRules(): array
    {
        return [
            'id' => [
                'type' => 'int'
            ],
            'LMRId' => [
                'required' => true,
                'type' => 'int'
            ],
            'status' => [
                'required' => true,
                'type' => 'string',
                'in' => [
                    DrawRequest::STATUS_NEW,
                    DrawRequest::STATUS_PENDING,
                    DrawRequest::STATUS_APPROVED,
                    DrawRequest::STATUS_REJECTED
                ]
            ],
            'sowApproved' => [
                'type' => 'int',
                'in' => [0, 1]
            ],
            'isDrawRequest' => [
                'type' => 'int',
                'in' => [0, 1]
            ],
            'amountRequested' => [
                'type' => 'numeric',
                'min' => 0
            ],
            'amountApproved' => [
                'type' => 'numeric',
                'min' => 0
            ],
            'rejectionReason' => [
                'type' => 'string',
                'maxLength' => 1000,
                'custom' => function ($value, $dto) {
                    if ($dto->status === DrawRequest::STATUS_REJECTED && empty($value)) {
                        return 'Rejection reason is required when status is rejected';
                    }
                    return true;
                }
            ],
            'lenderNotes' => [
                'type' => 'string',
                'maxLength' => 2000
            ]
        ];
    }

    /**
     * Override populateFromArray to handle categories
     * 
     * @param array $data Input data
     * @return void
     */
    protected function populateFromArray(array $data): void
    {
        parent::populateFromArray($data);
        
        // Handle categories array
        if (isset($data['categories']) && is_array($data['categories'])) {
            $this->categories = [];
            foreach ($data['categories'] as $categoryData) {
                if (is_array($categoryData)) {
                    $this->categories[] = CategoryData::fromArray($categoryData);
                } elseif ($categoryData instanceof CategoryData) {
                    $this->categories[] = $categoryData;
                }
            }
        }
    }

    /**
     * Override toArray to handle categories
     * 
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    public function toArray(bool $includeNulls = true): array
    {
        $result = parent::toArray($includeNulls);
        
        // Convert categories to arrays
        $result['categories'] = [];
        foreach ($this->categories as $category) {
            $result['categories'][] = $category->toArray($includeNulls);
        }
        
        return $result;
    }

    /**
     * Add category to draw request
     * 
     * @param CategoryData $category Category to add
     * @return void
     */
    public function addCategory(CategoryData $category): void
    {
        $this->categories[] = $category;
    }

    /**
     * Remove category by ID
     * 
     * @param int $categoryId Category ID to remove
     * @return bool True if removed, false if not found
     */
    public function removeCategory(int $categoryId): bool
    {
        foreach ($this->categories as $index => $category) {
            if ($category->id === $categoryId) {
                unset($this->categories[$index]);
                $this->categories = array_values($this->categories); // Re-index array
                return true;
            }
        }
        return false;
    }

    /**
     * Get category by ID
     * 
     * @param int $categoryId Category ID
     * @return CategoryData|null
     */
    public function getCategory(int $categoryId): ?CategoryData
    {
        foreach ($this->categories as $category) {
            if ($category->id === $categoryId) {
                return $category;
            }
        }
        return null;
    }

    /**
     * Get total cost of all categories
     * 
     * @return float
     */
    public function getTotalCost(): float
    {
        $total = 0.0;
        foreach ($this->categories as $category) {
            $total += $category->getTotalCost();
        }
        return $total;
    }

    /**
     * Get total completed amount of all categories
     * 
     * @return float
     */
    public function getTotalCompletedAmount(): float
    {
        $total = 0.0;
        foreach ($this->categories as $category) {
            $total += $category->getTotalCompletedAmount();
        }
        return $total;
    }

    /**
     * Get total requested amount of all categories
     * 
     * @return float
     */
    public function getTotalRequestedAmount(): float
    {
        $total = 0.0;
        foreach ($this->categories as $category) {
            $total += $category->getTotalRequestedAmount();
        }
        return $total;
    }

    /**
     * Sort categories by order
     * 
     * @return void
     */
    public function sortCategories(): void
    {
        usort($this->categories, function (CategoryData $a, CategoryData $b) {
            return ($a->order ?? 0) <=> ($b->order ?? 0);
        });
        
        // Also sort line items within each category
        foreach ($this->categories as $category) {
            $category->sortLineItems();
        }
    }

    /**
     * Check if draw request can be submitted
     * 
     * @return bool
     */
    public function canBeSubmitted(): bool
    {
        return $this->status === DrawRequest::STATUS_NEW && $this->getTotalRequestedAmount() > 0;
    }

    /**
     * Check if draw request can be approved
     * 
     * @return bool
     */
    public function canBeApproved(): bool
    {
        return $this->status === DrawRequest::STATUS_PENDING;
    }

    /**
     * Check if draw request can be rejected
     * 
     * @return bool
     */
    public function canBeRejected(): bool
    {
        return $this->status === DrawRequest::STATUS_PENDING;
    }

    /**
     * Get all line items across all categories
     * 
     * @return LineItemData[]
     */
    public function getAllLineItems(): array
    {
        $lineItems = [];
        foreach ($this->categories as $category) {
            $lineItems = array_merge($lineItems, $category->lineItems);
        }
        return $lineItems;
    }

    /**
     * Get line item by ID across all categories
     * 
     * @param int $lineItemId Line item ID
     * @return LineItemData|null
     */
    public function getLineItem(int $lineItemId): ?LineItemData
    {
        foreach ($this->categories as $category) {
            $lineItem = $category->getLineItem($lineItemId);
            if ($lineItem) {
                return $lineItem;
            }
        }
        return null;
    }
}
