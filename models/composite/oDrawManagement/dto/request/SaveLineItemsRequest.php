<?php

namespace models\composite\oDrawManagement\dto\request;

use models\composite\oDrawManagement\dto\base\ValidatableDTO;
use models\composite\oDrawManagement\dto\shared\LineItemData;

/**
 * Save Line Items Request DTO
 * 
 * Represents the request structure for saving line items
 */
class SaveLineItemsRequest extends ValidatableDTO
{
    public ?int $pcid = null;
    public ?int $lmrid = null;
    public bool $isDraft = false;
    
    /**
     * @var LineItemData[] Array of line items grouped by category
     */
    public array $lineItems = [];

    /**
     * Define validation rules
     * 
     * @return array
     */
    protected function getValidationRules(): array
    {
        return [
            'pcid' => [
                'type' => 'int',
                'custom' => function ($value, $dto) {
                    if (!$value && !$dto->lmrid) {
                        return 'Either pcid or lmrid must be provided';
                    }
                    return true;
                }
            ],
            'lmrid' => [
                'type' => 'int',
                'custom' => function ($value, $dto) {
                    if (!$value && !$dto->pcid) {
                        return 'Either pcid or lmrid must be provided';
                    }
                    return true;
                }
            ],
            'isDraft' => [
                'type' => 'bool'
            ],
            'lineItems' => [
                'required' => true,
                'type' => 'array',
                'custom' => function ($value, $dto) {
                    if (empty($value)) {
                        return 'At least one line item is required';
                    }
                    
                    // Validate each line item
                    foreach ($value as $categoryId => $categoryLineItems) {
                        if (!is_array($categoryLineItems)) {
                            return "Line items for category {$categoryId} must be an array";
                        }
                        
                        foreach ($categoryLineItems as $index => $lineItemData) {
                            if (!($lineItemData instanceof LineItemData)) {
                                return "Line item at category {$categoryId}, index {$index} must be a LineItemData instance";
                            }
                            
                            if (!$lineItemData->validate()) {
                                $errors = implode(', ', $lineItemData->getAllErrorMessages());
                                return "Line item at category {$categoryId}, index {$index} validation failed: {$errors}";
                            }
                        }
                    }
                    
                    return true;
                }
            ]
        ];
    }

    /**
     * Override populateFromArray to handle line items
     * 
     * @param array $data Input data
     * @return void
     */
    protected function populateFromArray(array $data): void
    {
        parent::populateFromArray($data);
        
        // Handle line items array
        if (isset($data['lineItems']) && is_array($data['lineItems'])) {
            $this->lineItems = [];
            foreach ($data['lineItems'] as $categoryId => $categoryLineItems) {
                if (is_array($categoryLineItems)) {
                    $this->lineItems[$categoryId] = [];
                    foreach ($categoryLineItems as $lineItemData) {
                        if (is_array($lineItemData)) {
                            $this->lineItems[$categoryId][] = LineItemData::fromArray($lineItemData);
                        } elseif ($lineItemData instanceof LineItemData) {
                            $this->lineItems[$categoryId][] = $lineItemData;
                        }
                    }
                }
            }
        }
    }

    /**
     * Override toArray to handle line items
     * 
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    public function toArray(bool $includeNulls = true): array
    {
        $result = parent::toArray($includeNulls);
        
        // Convert line items to arrays
        $result['lineItems'] = [];
        foreach ($this->lineItems as $categoryId => $categoryLineItems) {
            $result['lineItems'][$categoryId] = [];
            foreach ($categoryLineItems as $lineItem) {
                $result['lineItems'][$categoryId][] = $lineItem->toArray($includeNulls);
            }
        }
        
        return $result;
    }

    /**
     * Get the identifier (pcid or lmrid)
     * 
     * @return int|null
     */
    public function getIdentifier(): ?int
    {
        return $this->pcid ?? $this->lmrid;
    }

    /**
     * Get the identifier type
     * 
     * @return string|null 'pcid' or 'lmrid'
     */
    public function getIdentifierType(): ?string
    {
        if ($this->pcid) {
            return 'pcid';
        }
        if ($this->lmrid) {
            return 'lmrid';
        }
        return null;
    }

    /**
     * Check if this is a lender request (has pcid)
     * 
     * @return bool
     */
    public function isLenderRequest(): bool
    {
        return $this->pcid !== null;
    }

    /**
     * Check if this is a borrower request (has lmrid)
     * 
     * @return bool
     */
    public function isBorrowerRequest(): bool
    {
        return $this->lmrid !== null;
    }

    /**
     * Get all line items as flat array
     * 
     * @return LineItemData[]
     */
    public function getAllLineItems(): array
    {
        $allLineItems = [];
        foreach ($this->lineItems as $categoryLineItems) {
            $allLineItems = array_merge($allLineItems, $categoryLineItems);
        }
        return $allLineItems;
    }

    /**
     * Get line items for specific category
     * 
     * @param int $categoryId Category ID
     * @return LineItemData[]
     */
    public function getLineItemsForCategory(int $categoryId): array
    {
        return $this->lineItems[$categoryId] ?? [];
    }

    /**
     * Add line item to category
     * 
     * @param int $categoryId Category ID
     * @param LineItemData $lineItem Line item to add
     * @return void
     */
    public function addLineItem(int $categoryId, LineItemData $lineItem): void
    {
        if (!isset($this->lineItems[$categoryId])) {
            $this->lineItems[$categoryId] = [];
        }
        $this->lineItems[$categoryId][] = $lineItem;
    }

    /**
     * Get total requested amount across all line items
     * 
     * @return float
     */
    public function getTotalRequestedAmount(): float
    {
        $total = 0.0;
        foreach ($this->getAllLineItems() as $lineItem) {
            $total += $lineItem->requestedAmount ?? 0.0;
        }
        return $total;
    }

    /**
     * Get total cost across all line items
     * 
     * @return float
     */
    public function getTotalCost(): float
    {
        $total = 0.0;
        foreach ($this->getAllLineItems() as $lineItem) {
            $total += $lineItem->cost ?? 0.0;
        }
        return $total;
    }

    /**
     * Validate that requested amounts don't exceed remaining costs
     * 
     * @return array Array of validation errors
     */
    public function validateRequestedAmounts(): array
    {
        $errors = [];
        
        foreach ($this->getAllLineItems() as $lineItem) {
            $lineItemErrors = $lineItem->validateRequestedAmounts();
            if (!empty($lineItemErrors)) {
                $errors[] = "Line item '{$lineItem->name}': " . implode(', ', $lineItemErrors);
            }
        }
        
        return $errors;
    }

    /**
     * Check if request has any line items with requested amounts
     * 
     * @return bool
     */
    public function hasRequestedAmounts(): bool
    {
        foreach ($this->getAllLineItems() as $lineItem) {
            if ($lineItem->hasRequestedAmount()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Sync all line item requested values for consistency
     * 
     * @param string $basedOn Either 'amount' or 'percent'
     * @return void
     */
    public function syncAllRequestedValues(string $basedOn = 'amount'): void
    {
        foreach ($this->getAllLineItems() as $lineItem) {
            $lineItem->syncRequestedValues($basedOn);
        }
    }
}
