<?php

namespace models\composite\oDrawManagement\dto\base;

use ReflectionClass;
use ReflectionProperty;

/**
 * Base Data Transfer Object
 *
 * Provides common functionality for all DTOs including:
 * - Automatic property mapping from arrays
 * - Serialization to arrays/JSON
 * - Type conversion and validation
 * - Property change tracking
 */
abstract class BaseDTO
{
    /**
     * @var array Tracks which properties have been modified
     */
    private array $modifiedProperties = [];

    /**
     * @var array Original values for change tracking
     */
    private array $originalValues = [];

    /**
     * @var bool Whether to track property changes
     */
    private bool $trackChanges = false;

    /**
     * Create DTO instance from array data
     *
     * @param array $data Input data
     * @param bool $trackChanges Whether to enable change tracking
     * @return static Instance of the DTO
     */
    public static function fromArray(array $data, bool $trackChanges = false)
    {
        $instance = new static();
        $instance->trackChanges = $trackChanges;
        $instance->populateFromArray($data);

        if ($trackChanges) {
            $instance->originalValues = $instance->toArray();
        }

        return $instance;
    }

    /**
     * Populate DTO properties from array data
     *
     * @param array $data Input data
     * @return void
     */
    protected function populateFromArray(array $data): void
    {
        $reflection = new ReflectionClass($this);
        $properties = $reflection->getProperties(ReflectionProperty::IS_PUBLIC);

        foreach ($properties as $property) {
            $propertyName = $property->getName();

            if (array_key_exists($propertyName, $data)) {
                $value = $data[$propertyName];
                $convertedValue = $this->convertValue($value, $property);
                $this->setProperty($propertyName, $convertedValue);
            }
        }
    }

    /**
     * Convert value to appropriate type based on property type hint
     *
     * @param mixed $value Input value
     * @param ReflectionProperty $property Property reflection
     * @return mixed Converted value
     */
    protected function convertValue($value, ReflectionProperty $property)
    {
        if ($value === null) {
            return null;
        }

        $type = $property->getType();
        if (!$type) {
            return $value;
        }

        $typeName = $type->getName();

        return match ($typeName) {
            'int' => (int) $value,
            'float' => (float) $value,
            'string' => (string) $value,
            'bool' => (bool) $value,
            'array' => is_array($value) ? $value : [$value],
            default => $value
        };
    }

    /**
     * Set property value with change tracking
     *
     * @param string $propertyName Property name
     * @param mixed $value Property value
     * @return void
     */
    protected function setProperty(string $propertyName, $value): void
    {
        if ($this->trackChanges && property_exists($this, $propertyName)) {
            $oldValue = $this->$propertyName ?? null;
            if ($oldValue !== $value) {
                $this->modifiedProperties[$propertyName] = true;
            }
        }

        $this->$propertyName = $value;
    }

    /**
     * Convert DTO to array
     *
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    public function toArray(bool $includeNulls = true): array
    {
        $result = [];
        $reflection = new ReflectionClass($this);
        $properties = $reflection->getProperties(ReflectionProperty::IS_PUBLIC);

        foreach ($properties as $property) {
            $propertyName = $property->getName();
            $value = $this->$propertyName;

            if (!$includeNulls && $value === null) {
                continue;
            }

            if (is_object($value) && method_exists($value, 'toArray')) {
                $value = $value->toArray($includeNulls);
            } elseif (is_array($value)) {
                $value = $this->convertArrayToArray($value, $includeNulls);
            }

            $result[$propertyName] = $value;
        }

        return $result;
    }

    /**
     * Convert array of objects to array of arrays
     *
     * @param array $array Input array
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    protected function convertArrayToArray(array $array, bool $includeNulls): array
    {
        $result = [];

        foreach ($array as $key => $item) {
            if (is_object($item) && method_exists($item, 'toArray')) {
                $result[$key] = $item->toArray($includeNulls);
            } else {
                $result[$key] = $item;
            }
        }

        return $result;
    }

    /**
     * Convert DTO to JSON
     *
     * @param bool $includeNulls Whether to include null values
     * @return string
     */
    public function toJson(bool $includeNulls = true): string
    {
        return json_encode($this->toArray($includeNulls), JSON_THROW_ON_ERROR);
    }

    /**
     * Get list of modified properties
     *
     * @return array
     */
    public function getModifiedProperties(): array
    {
        return array_keys($this->modifiedProperties);
    }

    /**
     * Check if any properties have been modified
     *
     * @return bool
     */
    public function hasChanges(): bool
    {
        return !empty($this->modifiedProperties);
    }

    /**
     * Get only the modified properties as an array
     *
     * @return array
     */
    public function getChangedData(): array
    {
        if (!$this->trackChanges) {
            return $this->toArray();
        }

        $result = [];
        foreach ($this->getModifiedProperties() as $propertyName) {
            $result[$propertyName] = $this->$propertyName;
        }

        return $result;
    }

    /**
     * Reset change tracking
     *
     * @return void
     */
    public function resetChangeTracking(): void
    {
        $this->modifiedProperties = [];
        $this->originalValues = $this->toArray();
    }
}
