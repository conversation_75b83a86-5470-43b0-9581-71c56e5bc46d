<?php
namespace models\composite\oDrawManagement\traits;

/**
 * Trait PropertiesMapper
 *
 * Provides automated property mapping from database objects to class properties.
 * Reduces boilerplate code in setProperties methods across composite models.
 */
trait PropertiesMapper
{
    /**
     * Automatically map properties from a database object to class properties.
     * This method uses reflection to map properties with the same names.
     *
     * @param object $dbObject The database object to map from.
     * @param array $propertyMap Optional custom property mapping ['classProperty' => 'dbProperty'].
     * @param string|null $dbObjectProperty Optional property name to store the database object.
     * @return void
     */
    protected function mapPropertiesFromDbObject(
        object $dbObject,
        array $propertyMap = [],
        ?string $dbObjectProperty = null
    ): void {
        $reflection = new \ReflectionClass($this);
        $classProperties = $reflection->getProperties();
        $typeDefaults = ['int' => 0, 'float' => 0.00, 'string' => '', 'bool' => false];
        foreach ($classProperties as $property) {
            $propertyName = $property->getName();
            $type = $property->getType()->getName();
            // Skip the database object property itself
            if ($dbObjectProperty && $propertyName === $dbObjectProperty) {
                continue;
            }

            //Skip base class properties
            if ($property->getDeclaringClass()->getName() !== $reflection->getName()) {
                continue;
            }

            // Determine the source property name
            $sourceProperty = $propertyMap[$propertyName] ?? $propertyName;

            // Map the property if it exists in the database object
            if (property_exists($dbObject, $sourceProperty)) {
                $value = $dbObject->$sourceProperty;

                if ($value === null && in_array($type, array_keys($typeDefaults)) && $propertyName !== 'id') {
                    $value = $typeDefaults[$type];
                }

                $this->$propertyName = $value;
            }
        }

        // Store the database object if a property name is provided
        if ($dbObjectProperty && property_exists($this, $dbObjectProperty)) {
            $this->$dbObjectProperty = $dbObject;
        }
    }

    /**
     * Generic setProperties method that can be used by most composite models.
     *
     * @param object $dbObject The database object to map from.
     * @param array $customMapping Optional custom property mapping.
     * @param array $defaultValues Optional default values.
     * @return void
     */
    protected function setProperties(object $dbObject, array $customMapping = [], array $defaultValues = []): void
    {
        // Determine the database object property name based on class naming convention
        $className = (new \ReflectionClass($this))->getShortName();
        $dbObjectProperty = $this->getImplementingClassPropertyName($className);

        // Set default values for common properties
        $dbObjClass = get_class($dbObject);
        $this->mapPropertiesFromDbObject($dbObject, $customMapping, $dbObjectProperty);
    }

    /**
     * Determine the database object property name based on class naming conventions.
     *
     * @param string $className The class name.
     * @return string The expected database object property name.
     */
    private function getImplementingClassPropertyName(string $className): string
    {
        // Map class names to their expected database object property names
        $classToPropertyMap = [
            'DrawRequest' => 'drawRequest',
            'BorrowerDrawCategory' => 'category',
            'BorrowerDrawLineItem' => 'lineItem',
            'DrawRequestsHistory' => 'drawRequestHistory',
            'SowCategory' => 'category',
            'SowLineItem' => 'lineItem',
            'SowTemplate' => 'template',
        ];

        return $classToPropertyMap[$className] ?? strtolower($className);
    }
}
