<?php

namespace models\composite\oDrawManagement\validation;

use models\composite\oDrawManagement\dto\base\ValidatableDTO;
use models\composite\oDrawManagement\dto\shared\LineItemData;
use models\composite\oDrawManagement\dto\shared\CategoryData;
use models\composite\oDrawManagement\dto\shared\DrawRequestData;

/**
 * Property Validation System
 * 
 * Provides comprehensive validation for object properties including
 * type checking, range validation, and business rule enforcement
 */
class PropertyValidator
{
    /**
     * @var array Validation error messages
     */
    private array $errors = [];

    /**
     * Validate DTO object
     * 
     * @param ValidatableDTO $dto DTO to validate
     * @param array $context Additional context for validation
     * @return bool True if valid, false otherwise
     */
    public function validate(ValidatableDTO $dto, array $context = []): bool
    {
        $this->errors = [];
        
        // Run basic DTO validation
        if (!$dto->validate()) {
            $this->errors = array_merge($this->errors, $dto->getAllErrorMessages());
        }

        // Run business rule validation based on DTO type
        if ($dto instanceof LineItemData) {
            $this->validateLineItem($dto, $context);
        } elseif ($dto instanceof CategoryData) {
            $this->validateCategory($dto, $context);
        } elseif ($dto instanceof DrawRequestData) {
            $this->validateDrawRequest($dto, $context);
        }

        return empty($this->errors);
    }

    /**
     * Validate line item business rules
     * 
     * @param LineItemData $lineItem Line item to validate
     * @param array $context Validation context
     * @return void
     */
    private function validateLineItem(LineItemData $lineItem, array $context): void
    {
        // Validate requested amounts
        $requestedErrors = $lineItem->validateRequestedAmounts();
        $this->errors = array_merge($this->errors, $requestedErrors);

        // Validate cost is positive
        if ($lineItem->cost <= 0) {
            $this->errors[] = "Line item '{$lineItem->name}' must have a positive cost";
        }

        // Validate completed amount doesn't exceed cost
        if ($lineItem->completedAmount > $lineItem->cost) {
            $this->errors[] = "Line item '{$lineItem->name}' completed amount cannot exceed total cost";
        }

        // Validate percentages are consistent
        if ($lineItem->cost > 0) {
            $calculatedPercent = ($lineItem->completedAmount / $lineItem->cost) * 100;
            $tolerance = 0.1; // Allow small floating point differences
            
            if (abs($lineItem->completedPercent - $calculatedPercent) > $tolerance) {
                $this->errors[] = "Line item '{$lineItem->name}' completed amount and percent are inconsistent";
            }
        }

        // Validate order is positive
        if ($lineItem->order <= 0) {
            $this->errors[] = "Line item '{$lineItem->name}' order must be positive";
        }

        // Context-specific validations
        if (isset($context['maxRequestableAmount'])) {
            if ($lineItem->requestedAmount > $context['maxRequestableAmount']) {
                $this->errors[] = "Line item '{$lineItem->name}' requested amount exceeds maximum allowed";
            }
        }

        if (isset($context['allowNegativeAmounts']) && !$context['allowNegativeAmounts']) {
            if ($lineItem->requestedAmount < 0) {
                $this->errors[] = "Line item '{$lineItem->name}' requested amount cannot be negative";
            }
        }
    }

    /**
     * Validate category business rules
     * 
     * @param CategoryData $category Category to validate
     * @param array $context Validation context
     * @return void
     */
    private function validateCategory(CategoryData $category, array $context): void
    {
        // Validate category has line items
        if (empty($category->lineItems)) {
            $this->errors[] = "Category '{$category->categoryName}' must have at least one line item";
        }

        // Validate line item order uniqueness within category
        $orders = [];
        foreach ($category->lineItems as $lineItem) {
            if (in_array($lineItem->order, $orders)) {
                $this->errors[] = "Category '{$category->categoryName}' has duplicate line item orders";
                break;
            }
            $orders[] = $lineItem->order;
        }

        // Validate each line item
        foreach ($category->lineItems as $lineItem) {
            if (!$this->validate($lineItem, $context)) {
                // Errors are already added by validateLineItem
            }
        }

        // Validate order is positive
        if ($category->order <= 0) {
            $this->errors[] = "Category '{$category->categoryName}' order must be positive";
        }

        // Context-specific validations
        if (isset($context['maxCategories']) && $category->order > $context['maxCategories']) {
            $this->errors[] = "Category '{$category->categoryName}' order exceeds maximum allowed categories";
        }
    }

    /**
     * Validate draw request business rules
     * 
     * @param DrawRequestData $drawRequest Draw request to validate
     * @param array $context Validation context
     * @return void
     */
    private function validateDrawRequest(DrawRequestData $drawRequest, array $context): void
    {
        // Validate draw request has categories
        if (empty($drawRequest->categories)) {
            $this->errors[] = "Draw request must have at least one category";
        }

        // Validate category order uniqueness
        $orders = [];
        foreach ($drawRequest->categories as $category) {
            if (in_array($category->order, $orders)) {
                $this->errors[] = "Draw request has duplicate category orders";
                break;
            }
            $orders[] = $category->order;
        }

        // Validate each category
        foreach ($drawRequest->categories as $category) {
            if (!$this->validate($category, $context)) {
                // Errors are already added by validateCategory
            }
        }

        // Validate status transitions
        if (isset($context['currentStatus'])) {
            if (!$this->isValidStatusTransition($context['currentStatus'], $drawRequest->status)) {
                $this->errors[] = "Invalid status transition from '{$context['currentStatus']}' to '{$drawRequest->status}'";
            }
        }

        // Validate rejection reason when status is rejected
        if ($drawRequest->status === 'rejected' && empty($drawRequest->rejectionReason)) {
            $this->errors[] = "Rejection reason is required when status is rejected";
        }

        // Validate amounts
        if ($drawRequest->amountApproved > $drawRequest->amountRequested) {
            $this->errors[] = "Approved amount cannot exceed requested amount";
        }

        // Context-specific validations
        if (isset($context['maxTotalAmount']) && $drawRequest->getTotalRequestedAmount() > $context['maxTotalAmount']) {
            $this->errors[] = "Total requested amount exceeds maximum allowed";
        }
    }

    /**
     * Check if status transition is valid
     * 
     * @param string $fromStatus Current status
     * @param string $toStatus New status
     * @return bool
     */
    private function isValidStatusTransition(string $fromStatus, string $toStatus): bool
    {
        $validTransitions = [
            'new' => ['pending', 'rejected'],
            'pending' => ['approved', 'rejected'],
            'approved' => [], // Final state
            'rejected' => ['pending'] // Can be resubmitted
        ];

        return in_array($toStatus, $validTransitions[$fromStatus] ?? []);
    }

    /**
     * Get validation errors
     * 
     * @return array
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get first validation error
     * 
     * @return string|null
     */
    public function getFirstError(): ?string
    {
        return $this->errors[0] ?? null;
    }

    /**
     * Check if validation passed
     * 
     * @return bool
     */
    public function isValid(): bool
    {
        return empty($this->errors);
    }

    /**
     * Add custom validation error
     * 
     * @param string $error Error message
     * @return void
     */
    public function addError(string $error): void
    {
        $this->errors[] = $error;
    }

    /**
     * Clear validation errors
     * 
     * @return void
     */
    public function clearErrors(): void
    {
        $this->errors = [];
    }

    /**
     * Validate array of DTOs
     * 
     * @param array $dtos Array of ValidatableDTO objects
     * @param array $context Validation context
     * @return bool True if all valid, false otherwise
     */
    public function validateBatch(array $dtos, array $context = []): bool
    {
        $this->errors = [];
        $allValid = true;

        foreach ($dtos as $index => $dto) {
            if ($dto instanceof ValidatableDTO) {
                if (!$this->validate($dto, $context)) {
                    $allValid = false;
                    // Prefix errors with index for identification
                    $indexedErrors = array_map(
                        fn($error) => "Item {$index}: {$error}",
                        $this->errors
                    );
                    $this->errors = array_merge($this->errors, $indexedErrors);
                }
            }
        }

        return $allValid;
    }
}
