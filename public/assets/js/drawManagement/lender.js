/**
 * Lender-specific Draw Management Functionality
 */

DrawManagement.lender = {
    /**
     * Initialize borrower-specific functionality
     */
    init: function() {
        DrawManagement.config.pcid = $('#pcid').val();
        DrawManagement.config.dataKey = 'pcid';
        DrawManagement.config.dataUrl = `/backoffice/api_v2/draw_management/lender/SowCategories?pcid=${DrawManagement.config.pcid}`;
        DrawManagement.config.saveLineItemsSuccessMessage = 'Line items saved successfully!';
    },

    /**
     * Build line items JSON for saving
     */
    buildLineItemsJson: function() {
        // Use DataMapper for consistent data extraction and mapping if available
        if (typeof DataMapper !== 'undefined') {
            return DataMapper.buildLineItemsRequest(
                DrawManagement.elements.$lineItemCategoriesContainer,
                DrawManagement.config
            );
        }

        // Fallback to original implementation
        const groupedLineItems = {};

        DrawManagement.elements.$lineItemCategoriesContainer.find('.line-item-category-section').each(function() {
            const $categoryCard = $(this);
            const categoryId = $categoryCard.data('category-id');
            if (!categoryId) {
                return;
            }

            const itemsForThisCategory = [];
            $categoryCard.find('tbody.sortable tr.editable-line-item').each(function(index) {
                const $row = $(this);
                let lineItemId = $row.data('line-item-id');
                let name, description;

                // For new items, get data from inputs or data attributes if they were blurred
                if (typeof lineItemId === 'string' && lineItemId.startsWith('new_li_')) {
                    name = $row.find('.line-item-name-input').val().trim() || $row.data('new-name');
                    description = $row.find('.line-item-description-input').val().trim() || $row.data('new-description');

                    if (!name) {
                        return;
                    }
                    lineItemId = null;
                } else {
                    name = $row.find('.line-item-name-display').text().trim();
                    description = $row.find('.line-item-description-display').text().trim();
                    lineItemId = parseInt(lineItemId, 10);
                }

                itemsForThisCategory.push({
                    id: lineItemId,
                    categoryId: categoryId,
                    name: name,
                    description: description || '',
                    order: index + 1
                });
            });
            if (itemsForThisCategory.length === 0) {
                groupedLineItems[categoryId] = [];
            }
            groupedLineItems[categoryId] = itemsForThisCategory;
        });

        return {
            pcid: DrawManagement.config.pcid,
            lineItems: groupedLineItems
        };
    },

    renderLineItemRowsUI: function(lineItems, $tbodyElement) {
        const rowTemplate = document.getElementById('line-item-row-template');
        if (!rowTemplate) {
            console.error("line-item-row-template not found!");
            return;
        }

        if (lineItems && lineItems.length > 0) {
            lineItems.forEach(item => {
                const clone = rowTemplate.content.cloneNode(true);
                const $row = $(clone).find('tr');

                $row.attr('data-line-item-id', item.id);
                $row.find('.line-item-name-display').text(item.name);
                $row.find('.line-item-name-input').val(item.name);
                $row.find('.line-item-name-display').attr('title', item.description);
                $row.find('.line-item-description-input').val(item.description);
                $tbodyElement.append($row);
            });
        }
    },

    initTemplateSettings: function() {
        const settings = DrawManagement.currentTemplateData;
        $('#allowBorrowersAddEditCategoriesTog').prop('checked', settings.allowBorrowersAddEditCategories === 1);
        $('#allowBorrowersDeleteCategoriesTog').prop('checked', settings.allowBorrowersDeleteCategories === 1);
        $('#allowBorrowersAddEditLineItemsTog').prop('checked', settings.allowBorrowersAddEditLineItems === 1);
        $('#allowBorrowersDeleteLineItemsTog').prop('checked', settings.allowBorrowersDeleteLineItems === 1);
        $('#allowBorrowersSOWRevisionsTog').prop('checked', settings.allowBorrowersSOWRevisions === 1);
        $('#allowBorrowersExceedFinancedRehabCostOnRevisionTog').prop('checked', settings.allowBorrowersExceedFinancedRehabCostOnRevision === 1);
        $('#allowBorrowersAddEditCategories').val(settings.allowBorrowersAddEditCategories);
        $('#allowBorrowersDeleteCategories').val(settings.allowBorrowersDeleteCategories);
        $('#allowBorrowersAddEditLineItems').val(settings.allowBorrowersAddEditLineItems);
        $('#allowBorrowersDeleteLineItems').val(settings.allowBorrowersDeleteLineItems);
        $('#allowBorrowersSOWRevisions').val(settings.allowBorrowersSOWRevisions);
        $('#allowBorrowersExceedFinancedRehabCostOnRevision').val(settings.allowBorrowersExceedFinancedRehabCostOnRevision);
        $('#drawFee').val(settings.drawFee);
    }
};



