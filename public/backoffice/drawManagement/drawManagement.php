<?php

use models\PageVariables;
use models\cypher;
use models\standard\Strings;
use models\Request;

$pcId = isset(PageVariables::$PCID) ? cypher::encrypt(PageVariables::$PCID) : '';
if (empty($pcId) && $_REQUEST['pcid']) $pcId = REQUEST::GetClean('pcid');
Strings::includeMyScript(
    [
    '/assets/js/drawManagement/utils/ApiClient.js',
    '/assets/js/drawManagement/utils/DataMapper.js',
    '/assets/js/drawManagement/utils/Validation.js',
    '/assets/js/drawManagement/common.js',
    '/assets/js/drawManagement/lender.js',
    '/assets/js/3rdParty/sortable/Sortable.min.js',
    ]
);
Strings::includeMyCSS(['/assets/css/components/drawManagement.css']);

$showDrawManagementTab = PageVariables::PC()->drawManagement && PageVariables::PC()->enableDrawManagementV2;
if (!$showDrawManagementTab) {
    return;
}
$userType= 'lender';
?>

<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.0/Sortable.min.js"></script>
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card card-custom">
            <div class="card-header card-header-tabs-line bg-gray-100">
                <div class="card-title">
                    <h3 class="card-label">
                        Scope of Work Template
                    </h3>
                </div>
                <div class="card-toolbar">
                    <a href="javascript:void(0);" class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass" data-card-tool="toggle" data-section="workCategoriesCard" data-toggle="tooltip" data-placement="top" title="Toggle Card" data-original-title="Toggle Card">
                        <i class="ki icon-nm ki-arrow-down"></i>
                    </a>
                </div>
            </div>

            <?php require __DIR__ . '/partials/_draw-management-card.php'; ?>
        </div>
    </div>
    <div class="col-md-12 mb-4">
        <div class="card card-custom">
            <div class="card-header card-header-tabs-line bg-gray-100">
                <div class="card-title">
                    <h3 class="card-label">
                        Settings
                    </h3>
                </div>
                <div class="card-toolbar">
                    <a href="javascript:void(0);" class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass collapsed" data-card-tool="toggle" data-section="drawTemplateSettings" data-toggle="tooltip" data-placement="top" title="Toggle Card" data-original-title="Toggle Card">
                        <i class="ki icon-nm ki-arrow-down"></i>
                    </a>
                </div>
            </div>

            <?php require __DIR__ . '/partials/_draw-template-settings-card.php'; ?>
        </div>
    </div>
</div>

<div id="drawManagement">
    <input type="hidden" id="pcid" value="<?php echo $pcId; ?>">
</div>

<script>
    $(document).ready(function() {
        DrawManagement.init(`<?= $userType ?>`);
    });
</script>
