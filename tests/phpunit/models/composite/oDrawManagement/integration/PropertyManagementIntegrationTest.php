<?php

namespace tests\phpunit\models\composite\oDrawManagement\integration;

use PHPUnit\Framework\TestCase;
use models\composite\oDrawManagement\dto\request\SaveCategoriesRequest;
use models\composite\oDrawManagement\dto\response\ApiResponse;
use models\composite\oDrawManagement\dto\shared\CategoryData;
use models\composite\oDrawManagement\serialization\Serializer;
use models\composite\oDrawManagement\validation\PropertyValidator;

/**
 * Integration tests for the enhanced property management system
 */
class PropertyManagementIntegrationTest extends TestCase
{
    /**
     * Test complete workflow: Request -> Validation -> Processing -> Response
     */
    public function testCompleteWorkflow(): void
    {
        // 1. Create request data (simulating frontend input)
        $requestData = [
            'lmrid' => 123,
            'categories' => [
                [
                    'id' => null, // New category
                    'categoryName' => 'Foundation Work',
                    'description' => 'Foundation and structural work',
                    'order' => 1,
                    'lineItems' => [
                        [
                            'id' => null, // New line item
                            'name' => 'Concrete Foundation',
                            'description' => 'Pour concrete foundation',
                            'cost' => 5000.00,
                            'completedAmount' => 0.00,
                            'completedPercent' => 0.0,
                            'requestedAmount' => 2500.00,
                            'requestedPercent' => 50.0,
                            'order' => 1
                        ]
                    ]
                ],
                [
                    'id' => 1, // Existing category
                    'categoryName' => 'Electrical Work',
                    'description' => 'Electrical installation and wiring',
                    'order' => 2,
                    'lineItems' => [
                        [
                            'id' => 1, // Existing line item
                            'name' => 'Main Panel Installation',
                            'description' => 'Install main electrical panel',
                            'cost' => 3000.00,
                            'completedAmount' => 1500.00,
                            'completedPercent' => 50.0,
                            'requestedAmount' => 1000.00,
                            'requestedPercent' => 33.33,
                            'order' => 1
                        ]
                    ]
                ]
            ]
        ];

        // 2. Create and validate request DTO
        $request = SaveCategoriesRequest::fromArray($requestData, true); // Enable change tracking
        $this->assertInstanceOf(SaveCategoriesRequest::class, $request);

        $validator = new PropertyValidator();
        $isValid = $validator->validate($request);
        $this->assertTrue($isValid, 'Request validation failed: ' . implode(', ', $validator->getErrors()));

        // 4. Simulate processing - modify some data
        $request->categories[0]->categoryName = 'Foundation & Structural Work'; // Modified
        $request->categories[1]->lineItems[0]->requestedAmount = 1200.00; // Modified
        $request->categories[1]->lineItems[0]->requestedPercent = 40.0; // Modified

        // 6. Create response using serializer
        $responseData = Serializer::createDrawManagementResponse(
            $request->categories,
            ['edit' => true, 'delete' => false],
            ['totalCost' => 8000.00, 'totalRequested' => 3700.00]
        );

        $apiResponse = ApiResponse::success($responseData, 'Categories processed successfully');

        // 7. Verify response structure
        $this->assertInstanceOf(ApiResponse::class, $apiResponse);
        $this->assertTrue($apiResponse->success);
        $this->assertEquals('Categories processed successfully', $apiResponse->message);

        // 8. Serialize response to JSON (as would be sent to frontend)
        $jsonResponse = $apiResponse->toJson();
        $this->assertIsString($jsonResponse);

        $decodedResponse = json_decode($jsonResponse, true);
        $this->assertIsArray($decodedResponse);
        $this->assertTrue($decodedResponse['success']);
        $this->assertArrayHasKey('data', $decodedResponse);
    }

    /**
     * Test error handling and validation workflow
     */
    public function testErrorHandlingWorkflow(): void
    {
        // Create invalid request data
        $invalidRequestData = [
            'lmrid' => null, // Missing required field
            'categories' => [
                [
                    'categoryName' => '', // Invalid: empty name
                    'order' => -1, // Invalid: negative order
                    'lineItems' => [
                        [
                            'name' => 'Test Item',
                            'cost' => -100.00, // Invalid: negative cost
                            'requestedAmount' => 200.00, // Invalid: exceeds cost
                            'order' => 0 // Invalid: zero order
                        ]
                    ]
                ]
            ]
        ];

        // Attempt to create request DTO with validation
        try {
            $request = SaveCategoriesRequest::fromArray($invalidRequestData, false, true);
            $this->fail('Expected validation exception was not thrown');
        } catch (\InvalidArgumentException $e) {
            $this->assertStringContainsString('Validation failed', $e->getMessage());
        }

        // Create request without immediate validation
        $request = SaveCategoriesRequest::fromArray($invalidRequestData, false, false);

        // Validate manually
        $validator = new PropertyValidator();
        $isValid = $validator->validate($request);
        $this->assertFalse($isValid);

        $errors = $validator->getErrors();
        $this->assertNotEmpty($errors);

        // Create error response
        $errorResponse = ApiResponse::validationError($errors, 'Request validation failed');
        $this->assertFalse($errorResponse->success);
        $this->assertEquals('Request validation failed', $errorResponse->message);
        $this->assertEquals($errors, $errorResponse->errors);

        // Verify error response serialization
        $jsonResponse = $errorResponse->toJson();
        $decodedResponse = json_decode($jsonResponse, true);
        $this->assertFalse($decodedResponse['success']);
        $this->assertNotEmpty($decodedResponse['errors']);
    }

    /**
     * Test bidirectional data transformation
     */
    public function testBidirectionalTransformation(): void
    {
        // Start with array data (from frontend)
        $originalData = [
            'id' => 1,
            'categoryName' => 'Test Category',
            'description' => 'Test Description',
            'order' => 1,
            'lineItems' => [
                [
                    'id' => 1,
                    'name' => 'Test Item',
                    'cost' => 100.00,
                    'order' => 1
                ]
            ]
        ];

        // Convert to DTO
        $category = CategoryData::fromArray($originalData);
        $this->assertInstanceOf(CategoryData::class, $category);
        $this->assertEquals('Test Category', $category->categoryName);
        $this->assertCount(1, $category->lineItems);

        // Modify DTO
        $category->categoryName = 'Modified Category';
        $category->lineItems[0]->cost = 150.00;

        // Convert back to array
        $modifiedData = $category->toArray();
        $this->assertEquals('Modified Category', $modifiedData['categoryName']);
        $this->assertEquals(150.00, $modifiedData['lineItems'][0]['cost']);

        // Verify structure is preserved
        $this->assertArrayHasKey('id', $modifiedData);
        $this->assertArrayHasKey('lineItems', $modifiedData);
        $this->assertIsArray($modifiedData['lineItems']);
        $this->assertArrayHasKey('cost', $modifiedData['lineItems'][0]);
    }

    /**
     * Test batch operations with validation
     */
    public function testBatchOperations(): void
    {
        $categoriesData = [
            [
                'categoryName' => 'Category 1',
                'order' => 1,
                'lineItems' => [
                    ['name' => 'Item 1', 'cost' => 100.00, 'order' => 1]
                ]
            ],
            [
                'categoryName' => 'Category 2',
                'order' => 2,
                'lineItems' => [
                    ['name' => 'Item 2', 'cost' => 200.00, 'order' => 1]
                ]
            ],
            [
                'categoryName' => '', // Invalid
                'order' => 3,
                'lineItems' => [
                    ['name' => 'Item 3', 'cost' => -50.00, 'order' => 1] // Invalid cost
                ]
            ]
        ];

        // Batch deserialize
        $categories = Serializer::batchDeserialize($categoriesData, CategoryData::class);
        $this->assertCount(3, $categories);

        // Batch validate
        $validator = new PropertyValidator();
        $allValid = $validator->validateBatch($categories);
        $this->assertFalse($allValid); // Should fail due to invalid category

        $errors = $validator->getErrors();
        $this->assertNotEmpty($errors);

        // Batch serialize valid categories only
        $validCategories = array_slice($categories, 0, 2);
        $serializedData = Serializer::batchSerialize($validCategories);
        $this->assertCount(2, $serializedData);
        $this->assertEquals('Category 1', $serializedData[0]['categoryName']);
        $this->assertEquals('Category 2', $serializedData[1]['categoryName']);
    }

    /**
     * Test performance with large datasets
     */
    public function testPerformanceWithLargeDataset(): void
    {
        $startTime = microtime(true);

        // Create large dataset
        $categoriesData = [];
        for ($i = 1; $i <= 50; $i++) {
            $lineItems = [];
            for ($j = 1; $j <= 20; $j++) {
                $lineItems[] = [
                    'id' => ($i * 100) + $j,
                    'name' => "Line Item {$i}-{$j}",
                    'cost' => rand(100, 1000),
                    'order' => $j
                ];
            }

            $categoriesData[] = [
                'id' => $i,
                'categoryName' => "Category {$i}",
                'order' => $i,
                'lineItems' => $lineItems
            ];
        }

        // Batch deserialize
        $categories = Serializer::batchDeserialize($categoriesData, CategoryData::class);
        $this->assertCount(50, $categories);

        // Validate all
        $validator = new PropertyValidator();
        $allValid = $validator->validateBatch($categories);
        $this->assertTrue($allValid);

        // Serialize back
        $serializedData = Serializer::batchSerialize($categories);
        $this->assertCount(50, $serializedData);

        $endTime = microtime(true);
        $duration = $endTime - $startTime;

        // Should complete within reasonable time (adjust threshold as needed)
        $this->assertLessThan(5.0, $duration, 'Performance test took too long: ' . $duration . ' seconds');

        // Verify data integrity
        $this->assertEquals('Category 1', $serializedData[0]['categoryName']);
        $this->assertCount(20, $serializedData[0]['lineItems']);
        $this->assertEquals('Line Item 1-1', $serializedData[0]['lineItems'][0]['name']);
    }
}
