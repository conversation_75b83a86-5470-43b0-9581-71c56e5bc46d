<?php

namespace tests\phpunit\models\composite\oDrawManagement\serialization;

use PHPUnit\Framework\TestCase;
use models\composite\oDrawManagement\serialization\Serializer;
use models\composite\oDrawManagement\dto\shared\CategoryData;
use models\composite\oDrawManagement\dto\shared\LineItemData;
use models\composite\oDrawManagement\dto\shared\DrawRequestData;
use models\composite\oDrawManagement\dto\response\ApiResponse;
use models\composite\oDrawManagement\dto\response\DrawManagementResponse;

/**
 * Test cases for Serializer
 */
class SerializerTest extends TestCase
{
    /**
     * Test basic object serialization to array
     */
    public function testToArray(): void
    {
        $lineItem = new LineItemData();
        $lineItem->id = 1;
        $lineItem->name = 'Test Item';
        $lineItem->cost = 100.00;

        $array = Serializer::toArray($lineItem);

        $this->assertIsArray($array);
        $this->assertEquals(1, $array['id']);
        $this->assertEquals('Test Item', $array['name']);
        $this->assertEquals(100.00, $array['cost']);
    }

    /**
     * Test array of objects serialization
     */
    public function testArraySerialization(): void
    {
        $lineItem1 = new LineItemData();
        $lineItem1->id = 1;
        $lineItem1->name = 'Item 1';

        $lineItem2 = new LineItemData();
        $lineItem2->id = 2;
        $lineItem2->name = 'Item 2';

        $items = [$lineItem1, $lineItem2];
        $array = Serializer::toArray($items);

        $this->assertIsArray($array);
        $this->assertCount(2, $array);
        $this->assertEquals('Item 1', $array[0]['name']);
        $this->assertEquals('Item 2', $array[1]['name']);
    }

    /**
     * Test nested object serialization
     */
    public function testNestedObjectSerialization(): void
    {
        $lineItem = new LineItemData();
        $lineItem->id = 1;
        $lineItem->name = 'Test Item';

        $category = new CategoryData();
        $category->id = 1;
        $category->categoryName = 'Test Category';
        $category->lineItems = [$lineItem];

        $array = Serializer::toArray($category);

        $this->assertIsArray($array);
        $this->assertEquals('Test Category', $array['categoryName']);
        $this->assertIsArray($array['lineItems']);
        $this->assertCount(1, $array['lineItems']);
        $this->assertEquals('Test Item', $array['lineItems'][0]['name']);
    }

    /**
     * Test JSON serialization
     */
    public function testToJson(): void
    {
        $lineItem = new LineItemData();
        $lineItem->id = 1;
        $lineItem->name = 'Test Item';
        $lineItem->cost = 100.00;

        $json = Serializer::toJson($lineItem);
        $decoded = json_decode($json, true);

        $this->assertIsString($json);
        $this->assertIsArray($decoded);
        $this->assertEquals(1, $decoded['id']);
        $this->assertEquals('Test Item', $decoded['name']);
        $this->assertEquals(100.00, $decoded['cost']);
    }

    /**
     * Test deserialization from array
     */
    public function testFromArray(): void
    {
        $data = [
            'id' => 1,
            'name' => 'Test Item',
            'cost' => 100.00,
            'order' => 1
        ];

        $lineItem = Serializer::fromArray($data, LineItemData::class);

        $this->assertInstanceOf(LineItemData::class, $lineItem);
        $this->assertEquals(1, $lineItem->id);
        $this->assertEquals('Test Item', $lineItem->name);
        $this->assertEquals(100.00, $lineItem->cost);
        $this->assertEquals(1, $lineItem->order);
    }

    /**
     * Test deserialization from JSON
     */
    public function testFromJson(): void
    {
        $data = [
            'id' => 1,
            'categoryName' => 'Test Category',
            'order' => 1
        ];

        $json = json_encode($data);
        $category = Serializer::fromJson($json, CategoryData::class);

        $this->assertInstanceOf(CategoryData::class, $category);
        $this->assertEquals(1, $category->id);
        $this->assertEquals('Test Category', $category->categoryName);
        $this->assertEquals(1, $category->order);
    }

    /**
     * Test auto-detection of DTO type
     */
    public function testAutoDetection(): void
    {
        // Test DrawRequest detection
        $drawRequestData = [
            'LMRId' => 123,
            'status' => 'pending',
            'categories' => []
        ];

        $result = Serializer::autoDeserialize($drawRequestData);
        $this->assertInstanceOf(DrawRequestData::class, $result);

        // Test Category detection
        $categoryData = [
            'categoryName' => 'Test Category',
            'lineItems' => []
        ];

        $result = Serializer::autoDeserialize($categoryData);
        $this->assertInstanceOf(CategoryData::class, $result);

        // Test LineItem detection
        $lineItemData = [
            'name' => 'Test Item',
            'cost' => 100.00
        ];

        $result = Serializer::autoDeserialize($lineItemData);
        $this->assertInstanceOf(LineItemData::class, $result);
    }

    /**
     * Test API response creation
     */
    public function testCreateApiResponse(): void
    {
        $data = ['test' => 'data'];
        
        // Test success response
        $successResponse = Serializer::createApiResponse($data, true, 'Success message');
        
        $this->assertInstanceOf(ApiResponse::class, $successResponse);
        $this->assertTrue($successResponse->success);
        $this->assertEquals('Success message', $successResponse->message);
        $this->assertEquals($data, $successResponse->data);

        // Test error response
        $errors = ['Error 1', 'Error 2'];
        $errorResponse = Serializer::createApiResponse(null, false, 'Error message', $errors);
        
        $this->assertInstanceOf(ApiResponse::class, $errorResponse);
        $this->assertFalse($errorResponse->success);
        $this->assertEquals('Error message', $errorResponse->message);
        $this->assertEquals($errors, $errorResponse->errors);
    }

    /**
     * Test draw management response creation
     */
    public function testCreateDrawManagementResponse(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->id = 1;
        $drawRequest->LMRId = 123;
        $drawRequest->status = 'pending';

        $permissions = ['edit' => true, 'delete' => false];
        $summary = ['totalCost' => 1000.00];

        $response = Serializer::createDrawManagementResponse($drawRequest, $permissions, $summary);

        $this->assertInstanceOf(DrawManagementResponse::class, $response);
        $this->assertEquals($drawRequest, $response->drawRequest);
        $this->assertEquals($permissions, $response->permissions);
        $this->assertEquals($summary, $response->summary);
    }

    /**
     * Test batch serialization
     */
    public function testBatchSerialize(): void
    {
        $lineItem1 = new LineItemData();
        $lineItem1->id = 1;
        $lineItem1->name = 'Item 1';

        $lineItem2 = new LineItemData();
        $lineItem2->id = 2;
        $lineItem2->name = 'Item 2';

        $objects = ['first' => $lineItem1, 'second' => $lineItem2];
        $result = Serializer::batchSerialize($objects);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('first', $result);
        $this->assertArrayHasKey('second', $result);
        $this->assertEquals('Item 1', $result['first']['name']);
        $this->assertEquals('Item 2', $result['second']['name']);
    }

    /**
     * Test batch deserialization
     */
    public function testBatchDeserialize(): void
    {
        $dataArray = [
            'first' => [
                'id' => 1,
                'name' => 'Item 1',
                'cost' => 100.00,
                'order' => 1
            ],
            'second' => [
                'id' => 2,
                'name' => 'Item 2',
                'cost' => 200.00,
                'order' => 2
            ]
        ];

        $result = Serializer::batchDeserialize($dataArray, LineItemData::class);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('first', $result);
        $this->assertArrayHasKey('second', $result);
        $this->assertInstanceOf(LineItemData::class, $result['first']);
        $this->assertInstanceOf(LineItemData::class, $result['second']);
        $this->assertEquals('Item 1', $result['first']->name);
        $this->assertEquals('Item 2', $result['second']->name);
    }

    /**
     * Test null value handling in serialization
     */
    public function testNullValueHandling(): void
    {
        $lineItem = new LineItemData();
        $lineItem->id = 1;
        $lineItem->name = 'Test Item';
        $lineItem->description = null;

        // With nulls
        $arrayWithNulls = Serializer::toArray($lineItem, true);
        $this->assertArrayHasKey('description', $arrayWithNulls);
        $this->assertNull($arrayWithNulls['description']);

        // Without nulls
        $arrayWithoutNulls = Serializer::toArray($lineItem, false);
        $this->assertArrayNotHasKey('description', $arrayWithoutNulls);
    }

    /**
     * Test error handling in deserialization
     */
    public function testDeserializationErrorHandling(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Class NonExistentClass does not exist');

        Serializer::fromArray([], 'NonExistentClass');
    }

    /**
     * Test invalid JSON handling
     */
    public function testInvalidJsonHandling(): void
    {
        $this->expectException(\JsonException::class);

        Serializer::fromJson('invalid json', LineItemData::class);
    }

    /**
     * Test complex nested structure serialization
     */
    public function testComplexNestedStructure(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->id = 1;
        $drawRequest->LMRId = 123;

        $category = new CategoryData();
        $category->id = 1;
        $category->categoryName = 'Category 1';

        $lineItem = new LineItemData();
        $lineItem->id = 1;
        $lineItem->name = 'Line Item 1';
        $lineItem->cost = 100.00;

        $category->lineItems = [$lineItem];
        $drawRequest->categories = [$category];

        $array = Serializer::toArray($drawRequest);

        $this->assertIsArray($array);
        $this->assertEquals(123, $array['LMRId']);
        $this->assertIsArray($array['categories']);
        $this->assertEquals('Category 1', $array['categories'][0]['categoryName']);
        $this->assertIsArray($array['categories'][0]['lineItems']);
        $this->assertEquals('Line Item 1', $array['categories'][0]['lineItems'][0]['name']);
        $this->assertEquals(100.00, $array['categories'][0]['lineItems'][0]['cost']);
    }
}

// Create a separate test file for ChangeTracker
