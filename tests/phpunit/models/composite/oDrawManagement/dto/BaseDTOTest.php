<?php

namespace tests\phpunit\models\composite\oDrawManagement\dto;

use PHPUnit\Framework\TestCase;
use models\composite\oDrawManagement\dto\shared\CategoryData;
use models\composite\oDrawManagement\dto\shared\LineItemData;
use models\composite\oDrawManagement\dto\shared\DrawRequestData;

/**
 * Test cases for BaseDTO functionality
 */
class BaseDTOTest extends TestCase
{
    /**
     * Test basic DTO creation from array
     */
    public function testCreateFromArray(): void
    {
        $data = [
            'id' => 1,
            'categoryName' => 'Test Category',
            'description' => 'Test Description',
            'order' => 1
        ];

        $category = CategoryData::fromArray($data);

        $this->assertEquals(1, $category->id);
        $this->assertEquals('Test Category', $category->categoryName);
        $this->assertEquals('Test Description', $category->description);
        $this->assertEquals(1, $category->order);
    }

    /**
     * Test DTO serialization to array
     */
    public function testToArray(): void
    {
        $category = new CategoryData();
        $category->id = 1;
        $category->categoryName = 'Test Category';
        $category->description = 'Test Description';
        $category->order = 1;

        $array = $category->toArray();

        $this->assertIsArray($array);
        $this->assertEquals(1, $array['id']);
        $this->assertEquals('Test Category', $array['categoryName']);
        $this->assertEquals('Test Description', $array['description']);
        $this->assertEquals(1, $array['order']);
    }

    /**
     * Test DTO serialization to JSON
     */
    public function testToJson(): void
    {
        $category = new CategoryData();
        $category->id = 1;
        $category->categoryName = 'Test Category';

        $json = $category->toJson();
        $decoded = json_decode($json, true);

        $this->assertIsString($json);
        $this->assertEquals(1, $decoded['id']);
        $this->assertEquals('Test Category', $decoded['categoryName']);
    }

    /**
     * Test change tracking functionality
     */
    public function testChangeTracking(): void
    {
        $data = [
            'id' => 1,
            'categoryName' => 'Original Name',
            'description' => 'Original Description'
        ];

        $category = CategoryData::fromArray($data, true); // Enable change tracking

        // Initially no changes
        $this->assertFalse($category->hasChanges());
        $this->assertEmpty($category->getModifiedProperties());

        // Make a change
        $category->categoryName = 'Modified Name';

        // Should detect changes
        $this->assertTrue($category->hasChanges());
        $this->assertContains('categoryName', $category->getModifiedProperties());

        // Get only changed data
        $changedData = $category->getChangedData();
        $this->assertArrayHasKey('categoryName', $changedData);
        $this->assertEquals('Modified Name', $changedData['categoryName']);
    }

    /**
     * Test nested object handling
     */
    public function testNestedObjects(): void
    {
        $lineItemData = [
            'id' => 1,
            'name' => 'Test Line Item',
            'cost' => 100.00
        ];

        $categoryData = [
            'id' => 1,
            'categoryName' => 'Test Category',
            'lineItems' => [$lineItemData]
        ];

        $category = CategoryData::fromArray($categoryData);

        $this->assertCount(1, $category->lineItems);
        $this->assertInstanceOf(LineItemData::class, $category->lineItems[0]);
        $this->assertEquals('Test Line Item', $category->lineItems[0]->name);
        $this->assertEquals(100.00, $category->lineItems[0]->cost);
    }

    /**
     * Test type conversion
     */
    public function testTypeConversion(): void
    {
        $data = [
            'id' => '123',           // String to int
            'cost' => '99.99',       // String to float
            'order' => '5',          // String to int
            'categoryName' => 123    // Number to string
        ];

        $lineItem = LineItemData::fromArray($data);

        $this->assertIsInt($lineItem->id);
        $this->assertEquals(123, $lineItem->id);
        $this->assertIsFloat($lineItem->cost);
        $this->assertEquals(99.99, $lineItem->cost);
        $this->assertIsInt($lineItem->order);
        $this->assertEquals(5, $lineItem->order);
    }

    /**
     * Test null value handling
     */
    public function testNullValueHandling(): void
    {
        $data = [
            'id' => null,
            'categoryName' => 'Test',
            'description' => null
        ];

        $category = CategoryData::fromArray($data);

        $this->assertNull($category->id);
        $this->assertEquals('Test', $category->categoryName);
        $this->assertNull($category->description);

        // Test toArray with and without nulls
        $arrayWithNulls = $category->toArray(true);
        $arrayWithoutNulls = $category->toArray(false);

        $this->assertArrayHasKey('id', $arrayWithNulls);
        $this->assertArrayHasKey('description', $arrayWithNulls);
        $this->assertArrayNotHasKey('id', $arrayWithoutNulls);
        $this->assertArrayNotHasKey('description', $arrayWithoutNulls);
    }

    /**
     * Test validation integration
     */
    public function testValidationIntegration(): void
    {
        // Valid data
        $validData = [
            'categoryName' => 'Valid Category',
            'description' => 'Valid Description',
            'order' => 1
        ];

        $category = CategoryData::fromArray($validData);
        $this->assertTrue($category->validate());
        $this->assertEmpty($category->getValidationErrors());

        // Invalid data
        $invalidData = [
            'categoryName' => '', // Required field empty
            'order' => -1         // Invalid order
        ];

        $invalidCategory = CategoryData::fromArray($invalidData);
        $this->assertFalse($invalidCategory->validate());
        $this->assertNotEmpty($invalidCategory->getValidationErrors());
    }

    /**
     * Test complex draw request structure
     */
    public function testComplexDrawRequestStructure(): void
    {
        $drawRequestData = [
            'id' => 1,
            'LMRId' => 123,
            'status' => 'pending',
            'categories' => [
                [
                    'id' => 1,
                    'categoryName' => 'Category 1',
                    'order' => 1,
                    'lineItems' => [
                        [
                            'id' => 1,
                            'name' => 'Line Item 1',
                            'cost' => 100.00,
                            'order' => 1
                        ]
                    ]
                ]
            ]
        ];

        $drawRequest = DrawRequestData::fromArray($drawRequestData);

        $this->assertEquals(1, $drawRequest->id);
        $this->assertEquals(123, $drawRequest->LMRId);
        $this->assertEquals('pending', $drawRequest->status);
        $this->assertCount(1, $drawRequest->categories);
        
        $category = $drawRequest->categories[0];
        $this->assertInstanceOf(CategoryData::class, $category);
        $this->assertEquals('Category 1', $category->categoryName);
        $this->assertCount(1, $category->lineItems);
        
        $lineItem = $category->lineItems[0];
        $this->assertInstanceOf(LineItemData::class, $lineItem);
        $this->assertEquals('Line Item 1', $lineItem->name);
        $this->assertEquals(100.00, $lineItem->cost);
    }

    /**
     * Test array conversion with nested objects
     */
    public function testArrayConversionWithNestedObjects(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->id = 1;
        $drawRequest->LMRId = 123;
        $drawRequest->status = 'pending';

        $category = new CategoryData();
        $category->id = 1;
        $category->categoryName = 'Test Category';

        $lineItem = new LineItemData();
        $lineItem->id = 1;
        $lineItem->name = 'Test Line Item';
        $lineItem->cost = 100.00;

        $category->lineItems = [$lineItem];
        $drawRequest->categories = [$category];

        $array = $drawRequest->toArray();

        $this->assertIsArray($array);
        $this->assertIsArray($array['categories']);
        $this->assertIsArray($array['categories'][0]);
        $this->assertIsArray($array['categories'][0]['lineItems']);
        $this->assertIsArray($array['categories'][0]['lineItems'][0]);
        
        $this->assertEquals('Test Line Item', $array['categories'][0]['lineItems'][0]['name']);
        $this->assertEquals(100.00, $array['categories'][0]['lineItems'][0]['cost']);
    }
}
